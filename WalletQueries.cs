namespace nbg.ewallet.repository.dbQueries;

internal static class WalletQueries
{
    internal const string GetWalletByOwnerUserId =
        "SELECT WalletId, WalletName, ConnectedIban, RegistrationDate, VatNumber,OrganizationName, WalletAccount, WalletAccountCreatedAt, " +
        "OwnerUserId, OwnerCustomerCode, IsCorporateUser " +
        "FROM [EWallet].[dbo].[Wallets] " +
        "WHERE OwnerUserId = @OwnerUserId";

    internal const string GetWalletById =
        "SELECT WalletId, WalletName, ConnectedIban, RegistrationDate, VatNumber,OrganizationName, WalletAccount, WalletAccountCreatedAt, " +
        "OwnerUserId, OwnerCustomerCode, IsCorporateUser " +
        "FROM [EWallet].[dbo].[Wallets] " +
        "WHERE WalletId = @Id";

    internal const string InsertWallet =
        "INSERT INTO [EWallet].[dbo].[Wallets] " +
        "(WalletId, WalletName, RegistrationDate, VatNumber,OrganizationName, WalletAccount, WalletAccountCreatedAt, OwnerUserId, " +
        "OwnerCustomerCode, IsCorporateUser, TenantId, ConnectedIban) " +
        "VALUES " +
        "(@WalletId, @WalletName, @RegistrationDate, @VatNumber, @OrganizationName, @walletAccount, @WalletAccountCreatedAt, @OwnerUserId, " +
        "@OwnerCustomerCode, @IsCorporateUser, @TenantId, @ConnectedIban)";

    internal const string UpdateWalletAccount =
        "UPDATE [EWallet].[dbo].[Wallets] " +
        "SET WalletAccount = @walletaccount, WalletAccountCreatedAt = @walletCreationTime " +
        "where WalletId = @walletid";

    internal const string UpdateWalletSubscription =
        "UPDATE [EWallet].[dbo].[Wallets] " +
        "SET ActiveSubscriptionId = @subscriptionId" +
        "where WalletId = @walletid";

    internal const string SearchsWallet =
        "SELECT WalletId, WalletName, ConnectedIban, RegistrationDate, VatNumber,OrganizationName, WalletAccount, WalletAccountCreatedAt, " +
        "OwnerUserId, OwnerCustomerCode, IsCorporateUser " +
        "FROM [EWallet].[dbo].[Wallets] " +
        "WHERE 1=1";

    internal const string WithWalletAccount = " AND [WalletAccount] LIKE @Account";
    internal const string WithWalletName = " AND [WalletName] LIKE @WalletName";
    internal const string WithVatNumber = " AND [VatNumber] LIKE @VatNumber";
    internal const string WithOrganizationName = " AND [OrganizationName] LIKE @OrganizationName";

    internal const string GetWalletByAccount =
        "SELECT WalletId, WalletName, ConnectedIban, RegistrationDate, VatNumber,OrganizationName, WalletAccount, WalletAccountCreatedAt, " +
        "OwnerUserId, OwnerCustomerCode, IsCorporateUser " +
        "FROM [EWallet].[dbo].[Wallets] " +
        "WHERE WalletAccount = @WalletAccount";

    internal const string EditWalletName =
        "UPDATE [EWallet].[dbo].[Wallets] " +
        "SET WalletName = @walletName " +
        "where WalletId = @walletId";

    internal const string SetDefaultTopUpAccount =
        "UPDATE [EWallet].[dbo].[Wallets] " +
        "SET ConnectedIban = @ConnectedIban " +
        "where WalletId = @walletId";

    public const string GetAccountsConfigurationByWalletId = @"
        SELECT WalletId, IBAN, HiddenFlag, HiddenSince
        FROM WalletAccountsConfiguration
        WHERE WalletId = @WalletId;
    ";

    public const string UpsertAccountConfiguration = @"
        MERGE WalletAccountsConfiguration AS target
        USING (SELECT @WalletId AS WalletId, @IBAN AS IBAN) AS source
            ON target.WalletId = source.WalletId AND target.IBAN = source.IBAN
        WHEN MATCHED THEN
            UPDATE SET HiddenFlag = @HiddenFlag, HiddenSince = @HiddenSince
        WHEN NOT MATCHED THEN
            INSERT (WalletId, IBAN, HiddenFlag, HiddenSince)
            VALUES (@WalletId, @IBAN, @HiddenFlag, @HiddenSince);
    ";

    public const string DeleteAccountsConfigurationByWalletId = @"
        DELETE FROM WalletAccountsConfiguration
        WHERE WalletId = @WalletId;
    ";

    public const string DeleteAccountsConfigurationNotInList = @"
        DELETE FROM WalletAccountsConfiguration
        WHERE WalletId = @WalletId
        AND IBAN NOT IN @Ibans;
    ";
}
