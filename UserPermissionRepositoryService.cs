﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Dapper;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using nbg.ewallet.repository.dbQueries;
using Nbg.Ewallet.Repository.dbQueries;
using Nbg.Ewallet.Repository.Interfaces;
using Nbg.Ewallet.Repository.Interfaces.UnitOfWork;
using Nbg.Ewallet.Repository.Types;
using Nbg.Ewallet.Repository.Types.Configuration;

namespace Nbg.Ewallet.Repository;

public class UserPermissionRepositoryService : BaseRepositoryService, IUserPermissionsRepositoryService
{
    public UserPermissionRepositoryService(
        IUnitOfWork unitOfWork,
        IOptions<RepositorySettings> repositoryOptions,
        ILogger<UserPermissionRepositoryService> logger) : base(unitOfWork, repositoryOptions, logger) { }

    public async Task<RepoUserPermission?> FindOneByActiveAndUserIdAsync(string userId)
    {
        return await ExecuteInTransactionAsync(async (conn, tx) =>
        {
            var response = await conn.QueryFirstOrDefaultAsync<RepoUserPermission>(
                ProfileQueries.GetActiveUserPermissions,
                new { userid = userId },
                tx);
            return response;
        }, "A database error occurred while fetching active user permission by user ID.");
    }

    public async Task<List<RepoUserPermission>> FindAllByActiveAndUserIdAndWalletIdAsync(string userid, Guid walletid)
    {
        return await ExecuteInTransactionAsync(async (conn, tx) =>
        {
            var permissions = await conn.QueryAsync<RepoUserPermission>(
                WalletUserPermissionsQueries.GetActiveExistingUserPermissions,
                new { walletid, userid },
                tx);
            return permissions.ToList();
        }, "A database error occurred while fetching user permissions on wallet ID.");
    }

    public async Task ExpireExistingPermissions(Guid myWalletId, IEnumerable<RepoUserPermission> permissions)
    {
        await ExecuteInTransactionAsync(async (conn, tx) =>
        {
            foreach (var permission in permissions)
            {
                var existingPermission = await conn.QueryFirstOrDefaultAsync(
                    WalletUserPermissionsQueries.GetCurrentActiveUserPermission,
                    new { permission.UserId, WalletId = myWalletId, CurrentDate = DateTime.UtcNow },
                    tx);

                if (existingPermission != null)
                {
                    await conn.ExecuteAsync(WalletUserPermissionsQueries.ExpireUserPermission,
                        new { ID = existingPermission.Id, ExpirationDate = DateTime.UtcNow },
                        tx);
                }
            }
        }, "A database error occurred while expiring existing permissions.");
    }

    public async Task SaveAllAsync(List<RepoUserPermission> permissions)
    {
        await ExecuteInTransactionAsync(async (conn, tx) =>
        {
            await conn.ExecuteAsync(WalletUserPermissionsQueries.InsertUserPermission, permissions, tx);
        }, "A database error occurred while inserting user permissions.");
    }

    public async Task<RepoUserPermission> SaveAsync(RepoUserPermission userPermission)
    {
        return await ExecuteInTransactionAsync(async (conn, tx) =>
        {
            await conn.ExecuteAsync(WalletUserPermissionsQueries.UpdateUserPermission, userPermission, tx);
            return userPermission;
        }, "A database error occurred while updating user permission.");
    }

    public async Task<List<RepoUserPermission>> FindAllByActiveAndWalletIdAndUserId(Guid walletId, string userId)
    {
        return await ExecuteInTransactionAsync(async (conn, tx) =>
        {
            if (userId == null)
            {
                var permissions = await conn.QueryAsync<RepoUserPermission>(
                    WalletUserPermissionsQueries.GetActiveUserPermissions,
                    new { walletId },
                    tx);
                return permissions.ToList();
            }
            else
            {
                var permissions = await conn.QueryAsync<RepoUserPermission>(
                    WalletUserPermissionsQueries.GetActiveUserPermissionsForUser,
                    new { walletId, userId },
                    tx);
                return permissions.ToList();
            }
        }, "A database error occurred while fetching active user permissions.");
    }

    public async Task<List<RepoUserPermission>> FindAllByExpiredAndWalletIdAndUserIdAsync(Guid walletId, string userId)
    {
        return await ExecuteInTransactionAsync(async (conn, tx) =>
        {
            if (userId == null)
            {
                var permissions = await conn.QueryAsync<RepoUserPermission>(
                    WalletUserPermissionsQueries.GetAllExpiredUserPermissions,
                    new { walletId },
                    tx);
                return permissions.ToList();
            }

            var userPermissions = await conn.QueryAsync<RepoUserPermission>(
                WalletUserPermissionsQueries.GetAllExpiredUserPermissionsForUser,
                new { walletId, userId },
                tx);
            return userPermissions.ToList();

        }, "A database error occurred while fetching all user permissions.");
    }
}
