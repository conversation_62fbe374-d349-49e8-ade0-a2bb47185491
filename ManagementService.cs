using System.Threading.Tasks;
using AutoMapper;
using Microsoft.Extensions.Logging;
using nbg.ewallet.repository.types;
using nbg.netcore.consent.repository.types;
using nbg.netcore.consent.types;
using Nbg.Ewallet.Api.Implementation.Extensions;
using Nbg.Ewallet.Api.Interfaces;
using Nbg.Ewallet.Api.Types.Wallet;
using Nbg.Ewallet.Repository.Interfaces;
using Nbg.Ewallet.Repository.Types.AccountApi;
using Nbg.NetCore.HttpExceptions;
using Nbg.NetCore.Utilities;
using Nbg.OpenBanking.Utilities;
using Newtonsoft.Json;

namespace Nbg.Ewallet.Api.Implementation;

public partial class ManagementService : IManagementService
{
    private readonly IMapper _mapper;
    private readonly IMainFrameConnector _mainFrameConnector;
    private readonly IConsentsRepositoryService _consentsRepositoryService;
    private readonly IHttpContextRepositoryService _httpContextRepositoryService;
    private readonly IWalletRepositoryService _walletRepositoryService;
    private readonly IUserPermissionsRepositoryService _userPermissionsRepositoryService;
    private readonly ICreateAccountService _createAccountService;
    private readonly ICorporateApiClientService _corporateApiClientService;
    private readonly ISubscriptionApiClientService _subscriptionApiClientService;
    private readonly IAccountsApiClientService _accountsApiClientService;
    private readonly ICicsConnectorService _cicsConnectorService;
    private readonly ILogger<ManagementService> _logger;
    private readonly IValidationService _validationService;

    public ManagementService(
        IMapper mapper,
        IMainFrameConnector mainFrameConnector,
        IConsentsRepositoryService consentsRepositoryService,
        IHttpContextRepositoryService httpContextRepositoryService,
        IWalletRepositoryService walletRepositoryService,
        IUserPermissionsRepositoryService userPermissionsRepositoryService,
        ICreateAccountService createAccountService,
        ICorporateApiClientService corporateApiClientService,
        ISubscriptionApiClientService subscriptionApiClientService,
        IAccountsApiClientService accountsApiClientService,
        ICicsConnectorService cicsConnectorService,
        ILogger<ManagementService> logger,
        IValidationService validationService)
    {
        _mapper = mapper;
        _mainFrameConnector = mainFrameConnector;
        _consentsRepositoryService = consentsRepositoryService;
        _httpContextRepositoryService = httpContextRepositoryService;
        _walletRepositoryService = walletRepositoryService;
        _userPermissionsRepositoryService = userPermissionsRepositoryService;
        _createAccountService = createAccountService;
        _corporateApiClientService = corporateApiClientService;
        _subscriptionApiClientService = subscriptionApiClientService;
        _accountsApiClientService = accountsApiClientService;
        _cicsConnectorService = cicsConnectorService;
        _logger = logger;
        _validationService = validationService;
    }

    public async Task<AvailableAccountsResponse> GetUserAccountsAsync(AccountsRequest request)
    {
        var req = new AccountsFullRequest
        {
            UserID = request.UserID,
            OwnAccounts = true,
            IsCorporateUser = request.IsCorporateUser,
            IncludeBeneficiaries = false,
            IncludeStatements = false,
            CheckConnectToLoan = false
        };

        AccountsFullResponse accountsResponse = await _accountsApiClientService.GetAccountsFullAsync(new AccountsFullRequest { UserID = request.UserID, });
        _logger.LogError("accountsResponse: {Response}", JsonConvert.SerializeObject(accountsResponse));
        return accountsResponse.ToAvailableAccountsResponse();
    }

    public async Task<ConsentResponse> GetConsentAsync(RetrieveConsentRequest request)
    {
        if (request.ConsentId.Clear() == null)
        {
            throw new BadRequestException("Incorrect JSON Format");
        }

        var repoConsentFull = await _consentsRepositoryService.GetConsentAsync(request.ConsentId);
        var consent = _mapper.Map<EWalletConsent>(repoConsentFull);

        var (customerCode, userId) = GetUserContext();
        var (customerData, authCustomerData, customerProductDetails) = await GetCustomerData(customerCode, userId);
        var validatedCustomerData = await ValidateCustomerData(customerData, customerProductDetails, authCustomerData);

        return ToConsentResponse(consent, validatedCustomerData);
    }

    public async Task<ConsentResponse> AuthorizeConsentAsync(Types.Wallet.UpdateConsentRequest request)
    {
        ValidateRequest(request);
        var (customerCode, userId) = GetUserContext();

        // External API validations and data retrieval (outside transaction)
        await ValidateTopUpAccount(userId, request.DefaultAccount);
        var (customerData, authCustomerData, customerProductDetails) = await GetCustomerData(customerCode, userId);
        var validatedCustomerData = await ValidateCustomerData(customerData, customerProductDetails, authCustomerData);
        CheckCustomerEligibility(validatedCustomerData);

        // External API call to create account (outside transaction)
        var isCorporateUser = customerData.BasicInfo.Type == CustomerTypes.LegalEntity;
        var account = await CreateAndConnectAccountExternal(customerCode, userId, isCorporateUser);

        // External API call to create subscription (outside transaction)
        var consent = await GetAndValidateConsent(request.ConsentId);
        var subscriptionResponse = await CreateSubscriptionExternal(consent, customerCode, account);

        // All database operations in a single transaction
        return await ExecuteDatabaseOperationsInTransaction(request, userId, customerCode, validatedCustomerData, customerData, account, consent, subscriptionResponse.SubscriptionId);
    }


    public async Task<ConsentResponse> RejectConsentAsync(Types.Wallet.UpdateConsentRequest request)
    {
        if (request.ConsentId.Clear() == null)
        {
            throw new BadRequestException("Incorrect JSON Format");
        }

        var repoConsentFull = await _consentsRepositoryService.GetConsentAsync(request.ConsentId);
        var consent = _mapper.Map<EWalletConsent>(repoConsentFull);

        if (consent.Status != ConsentStatus.AwaitingAuthorization)
        {
            throw new BadRequestException($"Can not reject consent with status {consent.Status.EnumToString()}");
        }

        consent.Status = ConsentStatus.Rejected;
        var consentData = new EWalletConsentData { DefaultIban = request.Iban.Clear() ?? string.Empty };
        consent.ConsentData = consentData.GetSerializedObject();

        var updatingRepoConsent = _mapper.Map<RepoConsent>(consent);
        var updatedRepoConsentFull = await _consentsRepositoryService.UpdateConsentAsync(request.ConsentId, updatingRepoConsent);
        var updatedConsent = _mapper.Map<EWalletConsent>(updatedRepoConsentFull);

        return ToConsentResponse(updatedConsent);
    }

    private static ConsentResponse ToConsentResponse(EWalletConsent consent, CustomerData customerData = null)
    {
        var extraData = consent.ExtraData.GetDeserializedObject<EWalletConsentExtraData>();
        consent.Data = consent.ConsentData.GetDeserializedObject<EWalletConsentData>();

        return new ConsentResponse
        {
            ConsentId = consent.Id.ToString(),
            SandboxId = extraData.SandboxId,
            DefaultIban = consent.Data.DefaultIban,
            WalletName = consent.Data.WalletName,
            PlanId = consent.Data.PlanId,
            ValidationControls = customerData?.ValidationControls,
            HasMissingInformation = customerData?.HasMissingInformation,
            isFPbutNotBusiness = customerData?.isFPbutNotBusiness,
        };
    }
}
