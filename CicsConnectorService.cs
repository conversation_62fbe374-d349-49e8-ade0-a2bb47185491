using Microsoft.Extensions.Logging;
using Nbg.Ewallet.Repository.Interfaces;
using Nbg.Ewallet.Repository.Types;
using Nbg.Ewallet.Repository.Types.Exceptions;
using Nbg.Ewallet.Repository.Types.Extensions;
using Nbg.NetCore.Services.Cics.Http;
using Nbg.NetCore.Services.Cics.Http.Contract.Jsewalc;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Nbg.Ewallet.Repository;

public class CicsConnectorService : ICicsConnectorService
{
    private readonly ILogger<CicsConnectorService> _logger;
    private readonly IAuditableCicsConnector _cicsHttpConnector;

    public CicsConnectorService(
        ILogger<CicsConnectorService> logger,
        IAuditableCicsConnector cicsHttpConnector)
    {
        _logger = logger;
        _cicsHttpConnector = cicsHttpConnector;
    }

    public async Task<JsewalcResponsePayload> CreateCustomerSubscriptionCountersAsync(long customerCode, SubscriptionTier tier, string iban)
    {
        var req = new JsewalcRequestPayload
        {
            Action = "INSERT",
            IIp = customerCode,
            Account = iban,
            EwCategory = tier.ToString(),
        };
        CicsJsonResponse<JsewalcResponsePayload> response = await _cicsHttpConnector.JsewalcAsync(new CicsJsonRequestSimplify<JsewalcRequestPayload> { Payload = req });

        if (response.Exception != null)
        {
            _logger.LogError("CicsJsonRequest Exception in InitializeSubscriptionCountersAsync with code {ExceptionCode} and message {ExceptionDescr}", response.Exception.Code, response.Exception.Descr);

            throw new CicsGenericExeption($"{response.Exception.Code} {response.Exception.Descr}");
        }

        return response.Payload;
    }

    public async Task UpdateCustomerSubscriptionCountersAsync(string customerId, SubscriptionTier subscriptionTier)
    {
        _ = long.TryParse(customerId, out var iIp);
        var req = new JsewalcRequestPayload
        {
            Action = "UPDATE",
            IIp = iIp,
            EwCategory = subscriptionTier.ToString().ToUpper(),
        };
        CicsJsonResponse<JsewalcResponsePayload> response = await _cicsHttpConnector.JsewalcAsync(new CicsJsonRequestSimplify<JsewalcRequestPayload> { Payload = req });

        if (response.Exception == null)
        {
            return;
        }

        _logger.LogError("CicsJsonRequest Exception in UpdateCustomerSubscriptionCountersAsync with code {ExceptionCode} and message {ExceptionDescr}", response.Exception.Code, response.Exception.Descr);

        throw new CicsGenericExeption($"{response.Exception.Code} {response.Exception.Descr}");
    }

    public async Task ResetCustomerSubscriptionCountersAsync(string customerId)
    {
        _ = long.TryParse(customerId, out var iIp);
        var req = new JsewalcRequestPayload
        {
            Action = "RESET",
            IIp = iIp
        };
        CicsJsonResponse<JsewalcResponsePayload> response = await _cicsHttpConnector.JsewalcAsync(new CicsJsonRequestSimplify<JsewalcRequestPayload> { Payload = req });

        if (response.Exception == null)
        {
            return;
        }
        _logger.LogError("CicsJsonRequest Exception in UpdateCustomerSubscriptionCountersAsync with code {ExceptionCode} and message {ExceptionDescr}", response.Exception.Code, response.Exception.Descr);
        throw new CicsGenericExeption($"{response.Exception.Code} {response.Exception.Descr}");
    }

    

    public async Task<List<Subscription>> GetSubscriptionsCountersAsync()
    {
        var req = new JsewalcRequestPayload
        {

        };
        CicsJsonResponse<JsewalcResponsePayload> response = await _cicsHttpConnector.JsewalcAsync(new CicsJsonRequestSimplify<JsewalcRequestPayload> { Payload = req });

        if (response.Exception == null)
        {
            return [];
        }

        _logger.LogError("CicsJsonRequest Exception in GetCustomerSubscriptionCountersAsync with code {ExceptionCode} and message {ExceptionDescr}", response.Exception.Code, response.Exception.Descr);

        throw new CicsGenericExeption($"{response.Exception.Code} {response.Exception.Descr}");
    }

    public async Task<List<Counter>> GetCustomerSubscriptionCountersAsync(string customerId)
    {
        _ = long.TryParse(customerId, out var iIp);
        var req = new JsewalcRequestPayload
        {
            Action = "SELECT",
            IIp = iIp
        };
        CicsJsonResponse<JsewalcResponsePayload> response = await _cicsHttpConnector.JsewalcAsync(new CicsJsonRequestSimplify<JsewalcRequestPayload> { Payload = req });

        if (response.Exception == null)
        {
            return response.Payload.TypeRows.Select(x => new Counter
            {
                Type = x.TrnType.ToSubscriptionTransactionType(),
                Value = (int)x.Counter.GetValueOrDefault()
            }).ToList();
        }

        _logger.LogError("CicsJsonRequest Exception in GetCustomerSubscriptionCountersAsync with code {ExceptionCode} and message {ExceptionDescr}", response.Exception.Code, response.Exception.Descr);

        throw new CicsGenericExeption($"{response.Exception.Code} {response.Exception.Descr}");
    }
}
