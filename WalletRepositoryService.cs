using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dapper;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using nbg.ewallet.repository.dbQueries;
using Nbg.Ewallet.Repository;
using Nbg.Ewallet.Repository.Interfaces;
using Nbg.Ewallet.Repository.Interfaces.UnitOfWork;
using Nbg.Ewallet.Repository.Types;
using Nbg.Ewallet.Repository.Types.Configuration;
using Nbg.Ewallet.Repository.Types.Exceptions;

namespace nbg.ewallet.repository;

public class WalletRepositoryService : BaseRepositoryService, IWalletRepositoryService
{
    public WalletRepositoryService(
        IOptions<RepositorySettings> repositoryOptions,
        ILogger<WalletRepositoryService> logger,
        IUnitOfWork unitOfWork) : base(unitOfWork, repositoryOptions, logger) { }

    public async Task SaveAsync(RepoWallet wallet)
    {
        await ExecuteInTransactionAsync(async (conn, tx) =>
        {
            await conn.ExecuteAsync(WalletQueries.InsertWallet, wallet, tx);
        }, "A database error occurred while inserting a wallet.");
    }

    /// <summary>
    /// Retrieves a wallet by its unique identifier asynchronously.
    /// </summary>
    /// <remarks>This method executes within a database transaction. If the wallet is not found, a <see cref="WalletNotFoundException"/> is thrown.</remarks>
    /// <param name="walletId">The unique identifier of the wallet to retrieve. Cannot be null, empty, or consist only of whitespace.</param>
    /// <returns>A <see cref="RepoWallet"/> object representing the wallet with the specified identifier.</returns>
    /// <exception cref="WalletNotFoundException">Thrown if no wallet with the specified identifier is found.</exception>
    public async Task<RepoWallet> FindOneByIdAsync(string walletId)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(walletId, nameof(walletId));
        return await ExecuteInTransactionAsync(async (conn, tx) =>
        {
            var wallet = await conn.QueryFirstOrDefaultAsync<RepoWallet>(
                WalletQueries.GetWalletById,
                new { Id = walletId },
                tx);
            return wallet ?? throw new WalletNotFoundException();
        }, "A database error occurred while retrieving the wallet.");
    }

    public async Task<bool> ExistsByOwnerUserIdAsync(string userId)
    {
        return await ExecuteInTransactionAsync(async (conn, tx) =>
        {
            var wallet = await conn.QueryFirstOrDefaultAsync<RepoWallet>(
                WalletQueries.GetWalletByOwnerUserId,
                new { OwnerUserId = userId },
                tx);
            return wallet != null;
        }, "A database error occurred while checking if the wallet exists by user ID.");
    }

    /// <summary>
    /// Retrieves a wallet associated with the specified owner user ID.
    /// </summary>
    /// <param name="userId">The user ID of the wallet owner. Cannot be null, empty, or whitespace.</param>
    /// <returns>A <see cref="RepoWallet"/> object representing the wallet associated with the specified user ID.</returns>
    /// <exception cref="WalletNotFoundException">Thrown if no wallet is found for the specified user ID.</exception>
    public async Task<RepoWallet> FindOneByOwnerUserIdAsync(string userId)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(userId, nameof(userId));
        return await ExecuteInTransactionAsync(async (conn, tx) =>
        {
            var wallet = await conn.QueryFirstOrDefaultAsync<RepoWallet>(
                WalletQueries.GetWalletByOwnerUserId,
                new { OwnerUserId = userId },
                tx);
            return wallet ?? throw new WalletNotFoundException();
        }, "A database error occurred while retrieving the wallet by owner user ID.");
    }

    public async Task UpdateWalletAccountByWalletIdAsync(string walletId, string walletAccount)
    {
        await ExecuteInTransactionAsync(async (conn, tx) =>
        {
            await conn.ExecuteAsync(
                WalletQueries.UpdateWalletAccount,
                new { walletaccount = walletAccount, walletCreationTime = DateTime.UtcNow, walletid = walletId },
                tx);
        }, "A database error occurred while updating the wallet account.");
    }

    public async Task UpdateWalletSubscriptionByWalletIdAsync(Guid walletid, Guid subscriptionId)
    {
        await ExecuteInTransactionAsync(async (conn, tx) =>
        {
            await conn.ExecuteAsync(
                WalletQueries.UpdateWalletSubscription,
                new { subscriptionId, walletid },
                tx);
        }, "A database error occurred while updating the wallet account.");
    }

    public async Task<List<RepoWallet>> FindAllByParamsAsync(
        string vatNumber,
        string walletAccount,
        string walletName,
        string organizationName)
    {
        return await ExecuteInTransactionAsync(async (conn, tx) =>
        {
            var queryBuilder = new StringBuilder(WalletQueries.SearchsWallet);
            if (!string.IsNullOrWhiteSpace(vatNumber)) queryBuilder.Append(WalletQueries.WithVatNumber);
            if (!string.IsNullOrWhiteSpace(walletAccount)) queryBuilder.Append(WalletQueries.WithWalletAccount);
            if (!string.IsNullOrWhiteSpace(walletName)) queryBuilder.Append(WalletQueries.WithWalletName);
            if (!string.IsNullOrWhiteSpace(organizationName)) queryBuilder.Append(WalletQueries.WithOrganizationName);

            var query = queryBuilder.ToString();
            var whereObject = new { Account = walletAccount, WalletName = walletName, VatNumber = vatNumber, OrganizationName = organizationName };
            var wallets = await conn.QueryAsync<RepoWallet>(query, whereObject, tx);
            return wallets.ToList();
        }, "A database error occurred while searching for wallets.");
    }

    public async Task<RepoWallet?> FindOneByWalletAccountAsync(string account)
    {
        return await ExecuteInTransactionAsync(async (conn, tx) =>
        {
            return await conn.QueryFirstOrDefaultAsync<RepoWallet>(WalletQueries.GetWalletByAccount, new { WalletAccount = account }, tx);
        }, "A database error occurred while retrieving the wallet by account.");
    }

    public async Task<RepoWallet> UpdateWalletNameByWalletIdAsync(Guid walletId, string walletName)
    {
        return await ExecuteInTransactionAsync(async (conn, tx) =>
        {
            await conn.ExecuteAsync(WalletQueries.EditWalletName, new { WalletName = walletName, WalletId = walletId }, tx);
            return await conn.QuerySingleAsync<RepoWallet>(WalletQueries.GetWalletById, new { Id = walletId }, tx);
        }, "A database error occurred while updating the wallet name.");
    }

    public async Task<RepoWallet> SetDefaultTopUpAccount(Guid walletId, string defaultTopUpAccount)
    {
        return await ExecuteInTransactionAsync(async (conn, tx) =>
        {
            await conn.ExecuteAsync(WalletQueries.SetDefaultTopUpAccount, new { ConnectedIban = defaultTopUpAccount, WalletId = walletId }, tx);
            return await conn.QuerySingleAsync<RepoWallet>(WalletQueries.GetWalletById, new { Id = walletId }, tx);
        }, "A database error occurred while setting default top up account.");
    }

    public async Task<List<RepoConfigAccount>> GetAccountsConfigurationByWalletIdAsync(Guid walletId)
    {
        return await ExecuteInTransactionAsync(async (conn, tx) =>
        {
            var result = await conn.QueryAsync<RepoConfigAccount>(
                WalletQueries.GetAccountsConfigurationByWalletId,
                new { WalletId = walletId },
                tx
            );
            return result.ToList();
        }, "A database error occurred while retrieving accounts configuration.");
    }

    public async Task SetAccountsConfigurationByWalletIdAsync(Guid walletId, IReadOnlyCollection<RepoConfigAccount> updatedVisibleAccounts)
    {
        await ExecuteInTransactionAsync(async (conn, tx) =>
        {
            var ibans = updatedVisibleAccounts.Select(a => a.IBAN).ToArray();
            await conn.ExecuteAsync(
                WalletQueries.DeleteAccountsConfigurationNotInList,
                new { WalletId = walletId, Ibans = ibans },
                tx
            );

            foreach (var acc in updatedVisibleAccounts)
            {
                await conn.ExecuteAsync(
                    WalletQueries.UpsertAccountConfiguration,
                    new
                    {
                        WalletId = walletId,
                        acc.IBAN,
                        acc.HiddenFlag,
                        acc.HiddenSince
                    },
                    tx
                );
            }

            return 0;
        }, "A database error occurred while setting accounts configuration.");
    }

    /// <summary>
    /// Executes a database operation within a transaction. This method is exposed to allow
    /// the ManagementService to group multiple database operations in a single transaction.
    /// </summary>
    /// <typeparam name="T">The return type of the operation</typeparam>
    /// <param name="operation">The database operation to execute</param>
    /// <param name="errorMessage">Error message to use if the operation fails</param>
    /// <returns>The result of the operation</returns>
    public async Task<T> ExecuteInTransactionAsync<T>(Func<IDbConnection, IDbTransaction, Task<T>> operation, string errorMessage)
    {
        return await base.ExecuteInTransactionAsync(operation, errorMessage);
    }

}
